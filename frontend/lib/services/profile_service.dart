import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/profile_model.dart';
import '../utlis/app_config/app_config.dart';

class ProfileService {
  static final String baseUrl = AppConfig.baseUrl;

  // Get headers with authentication
  static Future<Map<String, String>> _getHeaders(
      {bool isMultipart = false}) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    Map<String, String> headers = {};

    if (!isMultipart) {
      headers['Content-Type'] = 'application/json';
    }

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get user profile
  static Future<ProfileModel?> getProfile() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/profile/get-profile'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['profile'] != null) {
          return ProfileModel.fromJson(data['profile']);
        }
      } else if (response.statusCode == 401) {
        throw Exception('Authentication failed. Please login again.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to fetch profile');
      }

      return null;
    } catch (e) {
      throw Exception('Network error: ${e.toString()}');
    }
  }

  // Update profile with multipart form data for images
  static Future<ProfileModel?> updateProfile({
    String? name,
    String? email,
    String? phoneNumber,
    String? shopOpenTime,
    String? shopCloseTime,
    File? profileImageFile,
    File? shopImageFile,
  }) async {
    try {
      final headers = await _getHeaders(isMultipart: true);

      var request = http.MultipartRequest(
        'PUT',
        Uri.parse('$baseUrl/profile/create-update-profile'),
      );

      // Add headers
      request.headers.addAll(headers);

      // Add text fields
      if (name != null && name.isNotEmpty) {
        request.fields['name'] = name;
      }
      if (email != null && email.isNotEmpty) {
        request.fields['email'] = email;
      }
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        request.fields['phoneNumber'] = phoneNumber;
      }
      if (shopOpenTime != null && shopOpenTime.isNotEmpty) {
        request.fields['shopOpenTime'] = shopOpenTime;
      }
      if (shopCloseTime != null && shopCloseTime.isNotEmpty) {
        request.fields['shopCloseTime'] = shopCloseTime;
      }

      // Add profile image file
      if (profileImageFile != null) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'profileImage',
            profileImageFile.path,
          ),
        );
      }

      // Add shop image file
      if (shopImageFile != null) {
        request.files.add(
          await http.MultipartFile.fromPath(
            'shopImage',
            shopImageFile.path,
          ),
        );
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['profile'] != null) {
          return ProfileModel.fromJson(data['profile']);
        }
      } else if (response.statusCode == 401) {
        throw Exception('Authentication failed. Please login again.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to update profile');
      }

      return null;
    } catch (e) {
      throw Exception('Network error: ${e.toString()}');
    }
  }
}
